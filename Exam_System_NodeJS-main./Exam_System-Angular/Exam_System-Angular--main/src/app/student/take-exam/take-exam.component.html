<div class="exam-container">
  <h2 class="exam-title">Take Exam</h2>
  <form #examForm="ngForm" (ngSubmit)="submitExam()" class="exam-form">
    <div class="question-card" *ngFor="let question of questions">
      <h3 class="question-text">{{ question.question }}</h3>
      <div class="options">
        <label *ngFor="let option of question.options" class="option-label">
          <input type="radio" [name]="'question' + question._id" [value]="option" (change)="onAnswerChange(question._id, option._id)" required>
          {{ option.text }}
        </label>
      </div>
    </div>
    <button type="submit" class="submit-btn">Submit Exam</button>
  </form>
</div>

<!-- Score Modal -->
<div class="score-modal-backdrop" *ngIf="showScoreModal">
  <div class="score-modal">
    <h2>Quiz Result</h2>
    <p class="score-text">You scored <span class="score-number">{{ score>=0 ? score : 0 }}</span> out of {{ questions.length }}</p>
    <button class="close-modal-btn" (click)="closeScoreModal()">Close</button>
  </div>
</div>