import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ExamService } from '../../services/exam.service';
import { Exam } from '../../models/exam.model';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [RouterModule, CommonModule],
  templateUrl: './dashboard.html',
  styleUrls: ['./dashboard.css']
})
export class DashboardComponent implements OnInit {
  exams: Exam[] = [];

  constructor(private examService: ExamService){}

  ngOnInit():void{
    const token = localStorage.getItem('token') || '';
    this.examService.getActiveExams(token).subscribe({
      next: (exams)=>{
        this.exams = exams;
      },
      error:(err)=>{
        console.log('Error loading exams:', err);
      }
    });
  }

  takeExam(examId: string) {
    // Navigate to take exam page
    window.location.href = `/student/take-exam/${examId}`;
  }
}
