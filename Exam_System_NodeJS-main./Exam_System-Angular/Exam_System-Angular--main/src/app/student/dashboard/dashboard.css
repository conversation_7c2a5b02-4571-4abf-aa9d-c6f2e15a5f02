.exam-card {
    background-color: #ffffff;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
    transition: transform 0.2s ease, box-shadow 0.4s ease;
    border: 1px solid #f0f0f0;
  }
  
  .exam-card:hover {    
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
  }
  
  .exam-title {
    font-weight: 700;
    font-size: 1.4rem;    
    color: rgb(135, 111, 201);;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .exam-description {
    color: #666;
    font-size: 0.95rem;
    margin-bottom: 1rem;
  }
  
  .exam-meta {
    font-size: 0.85rem;
    color: #555;
    display: flex;
    justify-content: space-between;
  }
  
  .exam-meta i {
    margin-right: 0.5rem;
  }
  
  .exam-button {
    background-color: #8b75c9;
    color: #fff;
    font-weight: 600;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: background 0.3s ease, transform 0.2s ease;
    display: inline-block;
    text-align: center;
    cursor: pointer;
  }
  
  .exam-button:hover {
    background-color: #7c3aed;
    transform: translateY(-1px);
    text-decoration: none;
  }
  
  .exam-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;
  }
  