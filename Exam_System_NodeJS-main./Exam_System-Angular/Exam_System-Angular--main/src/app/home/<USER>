body, .home-hero-bg {
  min-height: 100vh;
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #e9e6f8 0%, #f5f3fb 100%);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  overflow-x: hidden;
  box-sizing: border-box;
}

.home-hero-bg {
  padding: 48px 8vw 24px 8vw;
  width: 100%;
  max-width: 100vw;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  box-sizing: border-box;
}

.home-hero-card {
  background: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 6px 24px rgba(102, 126, 234, 0.12);
  padding: 5rem 4rem;
  max-width: 60vw;
  width: 60vw;
  min-width: 300px;
  min-height: 400px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid #dddaf0;
  box-sizing: border-box;
}

.home-hero-title {
  font-size: 2.2rem;
  font-weight: 800;
  margin-bottom: 1rem;
  letter-spacing: -0.5px;
  background: linear-gradient(90deg, #8b75c9 30%, #a292d6 70%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.home-hero-title span {
  color: #8b75c9;
  -webkit-text-fill-color: inherit;
}

.home-hero-desc {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2rem;
  font-weight: 500;
  line-height: 1.6;
}

.home-hero-buttons {
  display: flex;
  gap: 1.2rem;
  justify-content: center;
  width: 100%;
  flex-wrap: wrap;
}

.home-hero-btn {
  padding: 0.85rem 2.2rem;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 2rem;
  cursor: pointer;
  transition: background 0.3s, color 0.3s, box-shadow 0.3s;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.08);
}

.admin-btn {
  background: linear-gradient(90deg, #8b75c9 0%, #a292d6 100%);
  color: #fff;
}

.admin-btn:hover {
  background: linear-gradient(90deg, #a292d6 0%, #8b75c9 100%);
}

.student-btn {
  background: #fff;
  color: #8b75c9;
  border: 2px solid #a292d6;
}

.student-btn:hover {
  background: #a292d6;
  color: #fff;
}

@media (max-width: 900px) {
  .home-hero-card {
    max-width: 96vw;
    width: 96vw;
    padding: 2rem 1rem;
  }

  .home-hero-title {
    font-size: 1.6rem;
  }

  .home-hero-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .home-hero-bg {
    padding: 32px 4vw 20px;
  }
}

@media (max-width: 600px) {
  .home-hero-card {
    padding: 1.5rem 1rem;
  }

  .home-hero-title {
    font-size: 1.3rem;
  }

  .home-hero-buttons {
    gap: 0.7rem;
  }

  .home-hero-bg {
    padding: 24px 3vw 16px;
  }
}


body, .home-hero-bg {
  min-height: 75vh;
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #e9e6f8 0%, #f5f3fb 100%);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  overflow-x: hidden;
  box-sizing: border-box;
}

.home-hero-bg {
  padding: 48px 8vw 24px 8vw;
  width: 100%;
  max-width: 100vw;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  box-sizing: border-box;
}

.home-hero-card {
  background: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 6px 24px rgba(102, 126, 234, 0.12);
  padding: 5rem 4rem;
max-width: 60vw;
  width: 60vw;
  min-width: 300px;
  min-height: 400px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid #dddaf0;
  box-sizing: border-box;
}

.home-hero-title {
  font-size: 2.2rem;
  font-weight: 800;
  margin-bottom: 1rem;
  letter-spacing: -0.5px;
  background: linear-gradient(90deg, #8b75c9 30%, #a292d6 70%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.home-hero-title span {
  color: #8b75c9;
  -webkit-text-fill-color: inherit;
}

.home-hero-desc {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2rem;
  font-weight: 500;
  line-height: 1.6;
}

.home-hero-buttons {
  display: flex;
  gap: 1.2rem;
  justify-content: center;
  width: 100%;
  flex-wrap: wrap;
}

.home-hero-btn {
  padding: 0.85rem 2.2rem;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 2rem;
  cursor: pointer;
  transition: background 0.3s, color 0.3s, box-shadow 0.3s;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.08);
}

.admin-btn {
  background: linear-gradient(90deg, #8b75c9 0%, #a292d6 100%);
  color: #fff;
}

.admin-btn:hover {
  background: linear-gradient(90deg, #a292d6 0%, #8b75c9 100%);
}

.student-btn {
  background: #fff;
  color: #8b75c9;
  border: 2px solid #a292d6;
}

.student-btn:hover {
  background: #a292d6;
  color: #fff;
}

@media (max-width: 900px) {
  .home-hero-card {
    max-width: 96vw;
    width: 96vw;
    padding: 2rem 1rem;
  }

  .home-hero-title {
    font-size: 1.6rem;
  }

  .home-hero-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .home-hero-bg {
    padding: 32px 4vw 20px;
  }
}

@media (max-width: 600px) {
  .home-hero-card {
    padding: 1.5rem 1rem;
  }

  .home-hero-title {
    font-size: 1.3rem;
  }

  .home-hero-buttons {
    gap: 0.7rem;
  }

  .home-hero-bg {
    padding: 24px 3vw 16px;
  }
}


    .features-section {
      padding: var(--space-6) 0;
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      gap: var(--space-3);
    }
    
    .feature-card {
      background-color: white;
      border-radius: 8px;
      padding: var(--space-3);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }
    
    .feature-icon {
      font-size: 2.5rem;
      margin-bottom: var(--space-2);
    }
    
    .feature-title {
      margin-bottom: var(--space-2);
    }
    
    .feature-description {
      color: var(--neutral-600);
    }
    
    .cta-section {
      background-color: var(--secondary-light);
      padding: var(--space-5) 0;
      margin-top: var(--space-5);
    }
    
    .cta-content {
      text-align: center;
      max-width: 700px;
      margin: 0 auto;
    }
    
    .cta-title {
      font-size: 2rem;
      color: var(--neutral-900);
      margin-bottom: var(--space-2);
    }
    
    .cta-description {
      font-size: 1.2rem;
      margin-bottom: var(--space-3);
      color: var(--neutral-800);
    }
    
    .cta-buttons {
      display: flex;
      justify-content: center;
    }
    
    @media (min-width: 768px) {
      .features-grid {
        grid-template-columns: repeat(2, 1fr);
      }
      
      .hero-title {
        font-size: 3.5rem;
      }
    }
    
    @media (min-width: 1024px) {
      .features-grid {
        grid-template-columns: repeat(4, 1fr);
      }
      
      .hero-title {
        font-size: 4rem;
      }
    }


    .features-section {
  padding: 4rem 2rem;
  background-color: #f9f8fc;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.feature-card {
  background-color: #fff;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 6px 18px rgba(140, 130, 200, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(140, 130, 200, 0.15);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.feature-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.8rem;
  color: #8b75c9;
}

.feature-description {
  font-size: 1rem;
  color: #555;
  line-height: 1.6;
}

/* Responsive columns */
@media (min-width: 768px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
