.header-container {
  width: 100%;
  max-width: 100vw;
  min-height: 64px;
  background: linear-gradient(135deg, #8b75c9 0%, #a292d6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  overflow-x: hidden;
}

.modern-navbar {
  width: 100%;
  max-width: 100vw;
  min-height: 64px;
  margin: 0 auto;
  border-radius: 0;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.10);
  background: linear-gradient(135deg, #8b75c9 0%, #a292d6 100%) !important;
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  overflow-x: hidden;
}

.modern-navbar .container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 56px;
  padding: 0 1rem;
  box-sizing: border-box;
}

.brand-text {
  display: flex;
  flex-direction: column;
  text-decoration: none !important;
  color: white !important;
  transition: transform 0.3s ease;
  margin-right: 1.5rem;
  line-height: 1.1;
}

.brand-text:hover {
  transform: translateY(-2px);
  color: #f8f9fa !important;
}

.brand-name {
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: -0.5px;
  line-height: 1;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.brand-tagline {
  font-size: 0.7rem;
  font-weight: 300;
  opacity: 0.9;
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-top: -2px;
}

.nav-link-modern {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  font-size: 1rem;
  padding: 0.4rem 1rem !important;
  margin: 0 0.15rem;
  border-radius: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 80px;
  text-align: center;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-link-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-link-modern:hover::before {
  left: 100%;
}

.nav-link-modern:hover {
  color: #fff !important;
  background-color: rgba(255, 255, 255, 0.13);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

/* Active Link */
.active-link {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Dropdown Styling */
.modern-dropdown {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 15px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
  padding: 0.5rem 0;
  margin-top: 0.5rem !important;
}

.modern-dropdown .dropdown-item {
  color: rgba(255, 255, 255, 0.9) !important;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
  border-radius: 10px;
  margin: 0.25rem 0.5rem;
}

.modern-dropdown .dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
  transform: translateX(5px);
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .modern-navbar .container {
    flex-direction: column;
    align-items: stretch;
    padding: 0.5rem 0.5rem;
  }
  .navbar-nav {
    flex-direction: column;
    gap: 0.2rem;
    margin-top: 0.5rem;
  }
  .brand-text {
    margin: 0 0 0.5rem 0;
    text-align: center;
  }
}

@media (max-width: 900px) {
  .modern-navbar,
  .header-container {
    min-height: 56px;
    padding: 0 2vw;
  }

  .modern-navbar .container {
    max-width: 100vw;
    padding: 0 1rem;
  }
}

@media (max-width: 600px) {
  .modern-navbar,
  .header-container {
    min-height: 48px;
    padding: 0 2vw;
  }

  .modern-navbar .container {
    max-width: 100vw;
    padding: 0 1rem;
  }
}

/* Navbar Toggler */
.navbar-toggler {
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 10px !important;
  padding: 0.5rem !important;
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

.navbar-nav.ms-auto {
  display: flex !important;
  flex-direction: row !important;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  padding: 0;
  list-style: none;
}

.navbar-nav.ms-auto .nav-item {
  margin: 0;
  padding: 0;
  list-style: none;
}

