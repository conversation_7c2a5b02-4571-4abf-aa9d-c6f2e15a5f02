/* Gradient text for header */
.text-gradient {
    background: linear-gradient(90deg, rgba(102, 126, 234), rgba(118, 75, 162));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  /* Gradient button */
  .btn-gradient {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    transition: all 0.3s ease-in-out;
    font-weight: 600;
    border-radius: 50px;
    padding: 0.5rem 1rem;
  }
  
  .btn-gradient:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.5);
    transform: translateY(-1px);
  }
  
  /* Background gradient */
  .bg-gradient {
    background: linear-gradient(to right, #667eea, #764ba2);
  }

  .bg-indigo-gradient {
    background: linear-gradient(135deg, #667eea, #764ba2);
  }

  .form-control:focus {
    border-color: #5a67d8;
    box-shadow: 0 0 0 0.2rem rgba(90, 103, 216, 0.25);
  }
  
  .login-error {
    color: #e53e3e;
    background: #fff5f5;
    padding: 0.75rem;
    border: 1px solid #feb2b2;
    border-radius: 0.5rem;
    margin: 1rem;
  }