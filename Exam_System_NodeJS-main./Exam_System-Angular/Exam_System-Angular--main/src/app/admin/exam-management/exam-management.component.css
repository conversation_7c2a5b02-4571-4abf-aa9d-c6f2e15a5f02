.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.exams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 1rem;
  margin-top: 2rem;
}

.exam-card {
  background: #fff;
  border-radius: 1.5rem;
  box-shadow: 0 6px 24px rgba(102, 126, 234, 0.12);
  padding: 2rem;
  transition: transform 0.3s, box-shadow 0.3s;
  margin-bottom: 2rem;
}

.exam-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-2);
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #4a4a4a;
}

.exam-header h3 {
  margin: 0;
  padding-right: var(--space-2);
}

.exam-description {
  font-size: 1rem;
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
  display: -webkit-box;
 
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.exam-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #444;
}

.stat {
  background: var(--neutral-50);
  padding: var(--space-2);
  border-radius: 4px;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: var(--neutral-600);
  margin-bottom: 0.25rem;
}

.stat-value {
  font-weight: 500;
  color: var(--neutral-900);
}


.button {
  background: linear-gradient(90deg, #8b75c9 0%, #a292d6 100%);
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s, box-shadow 0.3s;
}

.button:hover {
  background: linear-gradient(90deg, #a292d6 0%, #8b75c9 100%);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.button-outline {
  background: #fff;
  color: #8b75c9;
  border: 2px solid #8b75c9;
  transition: background 0.3s, color 0.3s;
}

.button-outline:hover {
  background: #8b75c9;
  color: #fff;
}
.action-row {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}
.exam-actions {
  display: flex;
flex-direction: column;
flex-wrap: wrap;
  justify-content: center; 
  align-items: center;
  gap: 1rem; 
  margin-top: 1rem;
  align-items: flex-start


}
