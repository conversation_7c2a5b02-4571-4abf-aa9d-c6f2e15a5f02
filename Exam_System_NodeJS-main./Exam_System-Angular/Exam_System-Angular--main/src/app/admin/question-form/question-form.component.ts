import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ExamService } from '../../services/exam.service';
import { Exam, Question } from '../../models/exam.model';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-question-form',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './question-form.html',
  styleUrls: ['./question-form.css']
})
export class QuestionFormComponent implements OnInit {
  examId: string = '';
  exam: Exam | null = null;
  questions: Question[] = [];
  newQuestion: Partial<Question> = { text: '', type: 'multiple-choice', options: [], points: 1 };

  constructor(private route: ActivatedRoute, private examService: ExamService) {}

  ngOnInit(): void {
    this.examId = this.route.snapshot.paramMap.get('examId') || '';
    const token = localStorage.getItem('token') || '';
    if (this.examId) {
      this.examService.getExamById(this.examId, token).subscribe(exam => {
        this.exam = exam;
        this.questions = exam.questions || [];
      });
    }
  }

  addOption() {
    if (!this.newQuestion.options) this.newQuestion.options = [];
    this.newQuestion.options.push({ text: '', isCorrect: false, id: '' });
  }

  removeOption(index: number) {
    if (this.newQuestion.options) this.newQuestion.options.splice(index, 1);
  }

  addQuestion() {
    const token = localStorage.getItem('token') || '';
    if (!this.examId || !this.newQuestion.text || !this.newQuestion.type || !this.newQuestion.points) return;
    const options = (this.newQuestion.options || []).map(opt => ({
      text: opt.text,
      isCorrect: !!opt.isCorrect
    }));
    const payload: any = {
      question: this.newQuestion.text,
      options: options,
      type: this.newQuestion.type,
      points: this.newQuestion.points
    };
    this.examService.addQuestion(this.examId, payload, token).subscribe({
      next: (res) => {
        this.questions.push({
          id: res.data._id,
          examId: this.examId,
          text: res.data.question,
          type: res.data.type,
          options: res.data.options,
          points: res.data.points
        });
        this.newQuestion = { text: '', type: 'multiple-choice', options: [], points: 1 };
      },
      error: (err) => {
        alert('Failed to add question.');
      }
    });
  }

  updateQuestion(question: Question) {
    const token = localStorage.getItem('token') || '';
    this.examService.updateQuestion(this.examId, question.id, question, token).subscribe({
      next: (updatedQuestion: Question) => {
        const index = this.questions.findIndex(q => q.id === question.id);
        if (index !== -1) this.questions[index] = updatedQuestion;
      },
      error: (error: any) => {
        alert('Failed to update question.');
        console.error('Error updating question:', error);
      }
    });
  }

  deleteQuestion(questionId: string) {
    if (confirm('Are you sure you want to delete this question?')) {
      const token = localStorage.getItem('token') || '';
      this.examService.deleteQuestion(this.examId, questionId, token).subscribe({
        next: () => {
          this.questions = this.questions.filter(q => q.id !== questionId);
        },
        error: (err) => {
          alert('Failed to delete question.');
        }
      });
    }
  }
}