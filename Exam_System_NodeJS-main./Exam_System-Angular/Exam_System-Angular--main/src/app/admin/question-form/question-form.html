<div class="container">
    <h2>Manage Questions for Exam</h2>
    <div *ngIf="exam">
        <h3>{{ exam.title }}</h3>
        <ul>
            <li *ngFor="let q of questions">
                <b>{{ q.text }}</b> ({{ q.type }})
                <button (click)="updateQuestion(q)">Edit</button>
                <button (click)="deleteQuestion(q.id)">Delete</button>
            </li>
        </ul>
        <form (ngSubmit)="addQuestion()" #questionForm="ngForm" class="question-form">
            <h4>Add New Question</h4>
            <div class="form-group">
                <label>Question Text</label>
                <input [(ngModel)]="newQuestion.text" name="text" required class="form-control"
                    placeholder="Question text">
            </div>
            <div class="form-group">
                <label>Type</label>
                <select [(ngModel)]="newQuestion.type" name="type" required class="form-control">
                    <option value="multiple-choice">Multiple Choice</option>
                    <option value="single-choice">Single Choice</option>
                    <option value="true-false">True/False</option>
                </select>
            </div>
            <div class="form-group">
                <label>Points</label>
                <input [(ngModel)]="newQuestion.points" name="points" type="number" min="1" required
                    class="form-control" placeholder="Points">
            </div>
            <!-- Optionally, add options for multiple-choice -->
            <div *ngIf="newQuestion.type === 'multiple-choice' || newQuestion.type === 'single-choice'">
                <label>Options</label>
                <div *ngFor="let opt of newQuestion.options ?? []; let i = index" class="option-row">
                    <input [(ngModel)]="opt.text" name="optionText{{i}}" placeholder="Option text" class="form-control">
                    <input type="checkbox" [(ngModel)]="opt.isCorrect" name="optionCorrect{{i}}">
                    Correct
                    <button type="button" (click)="removeOption(i)">Remove</button>
                </div>
                <button type="button" (click)="addOption()">Add Option</button>
            </div>
            <button type="submit" [disabled]="!questionForm.form.valid" class="btn btn-primary mt-2">Add
                Question</button>
        </form>
    </div>
    <div *ngIf="!exam">Loading exam...</div>
</div>