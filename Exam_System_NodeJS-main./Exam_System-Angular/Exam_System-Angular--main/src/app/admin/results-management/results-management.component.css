:root {
  --blue: #3b82f6;
  --green: #22c55e;
  --purple: #8b5cf6;
  --text-dark: #1e293b;
  --text-light: #64748b;
  --bg-card: #ffffff;
  --bg-section: #f9fafb;
  --border: #e2e8f0;
}

body {
  background-color: var(--bg-section);
  font-family: 'Inter', sans-serif;
  margin: 0;
  padding: 20px;
  color: var(--text-dark);
}

.page-header {
  margin-top: 3rem;
  margin-bottom: 2rem;
}

.results-container {
  max-width: 1200px;
  margin: auto;
  margin-top: 1.5rem;
}

.heading {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 30px;
  font-weight: 600;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.card {
  background: var(--bg-card);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: transform 0.2s ease;
  border: 1px solid var(--border);
}

.card:hover {
  transform: translateY(-6px);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border);
  background-color: #f1f5f9;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-dark);
}

.card-title i {
  font-size: 1.3rem;
  color: var(--text-light);
}

.card-content {
  padding: 20px;
}

.stat {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.stat.blue { color: var(--blue); }
.stat.green { color: var(--green); }
.stat.purple { color: var(--purple); }

.note {
  font-size: 0.95rem;
  color: var(--text-light);
}

.full-width {
  grid-column: 1 / -1;
}

.center {
  text-align: center;
}

.icon-large {
  font-size: 3.5rem;
  color: #cbd5e1;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 1.3rem;
  margin-bottom: 5px;
  font-weight: 500;
}

.exam-cards-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr); 
  gap: 24px;
  margin-top: 24px;
}

.exam-card {
  background-color: var(--bg-card);
  border-radius: 18px;
  box-shadow: 0 4px 18px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border);
  padding: 16px 20px;
  transition: transform 0.25s ease, box-shadow 0.25s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 280px;
}

.exam-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.08);
}

.exam-card h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-dark);
  display: flex;
  align-items: center;
  gap: 6px;
}

.exam-card p {
  font-size: 0.9rem;
  color: var(--text-light);
  margin: 6px 0;
  padding: 4px 0;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px dashed var(--border);
}

.exam-card p:last-of-type {
  border-bottom: none;
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-indicator-text {
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 50px;
  padding: 4px 10px;
  border: 1.5px solid;
  background-color: rgba(0, 0, 0, 0.03);
  text-transform: uppercase;
}

.status-indicator-text.pass {
  color: var(--green);
  border-color: var(--green);
  background-color: rgba(34, 197, 94, 0.1);
}

.status-indicator-text.fail {
  color: #ef4444;
  border-color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

