body {
    font-family: 'Segoe UI', sans-serif;
    margin: 0;
    padding: 2rem;
    background-color: #f7f9fc;
    color: #1a202c;
  }

  h2 {
    color: #5a67d8;
  }

  .dashboard-container {
    max-width: 1200px;
    margin: auto;
  }

  .stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
  }

  .stat-card {
    background-color: #fff;
    padding: 1.5rem;
    flex: 1;
    min-width: 250px;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .stat-card h4 {
    margin: 0 0 0.5rem;
  }

  .stat-number {
    font-size: 1.5rem;
    font-weight: bold;
  }

  .stat-number.blue { color: #3b82f6; }
  .stat-number.green { color: #22c55e; }
  .stat-number.purple { color: #a855f7; }

  .quick-actions {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
  }

  .action-box, .activity-box {
    flex: 1;
    background-color: #fff;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .action-box h4,
  .activity-box h4 {
    margin-bottom: 1rem;
  }

  .action-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: #f1f5f9;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    text-decoration: none;
    color: #1a202c;
    font-weight: 500;
  }

  .action-link i {
    margin-right: 0.75rem;
  }

  .action-link:hover{
    background-color: #667eea;
    color: white;
  }
 
  /* .primary-action{
    background-color: #667eea;
    color: white;
  } */

  
  .recent-activity-list {
    max-height: 200px; /* or any height you prefer */
    overflow-y: auto;
    padding-right: 0.5rem;
    margin: 0;
    list-style: none;
  }
  
  .recent-activity-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .recent-activity-list::-webkit-scrollbar {
    width: 6px;
  }
  
  .recent-activity-list::-webkit-scrollbar-thumb {
    background-color: #cbd5e0;
    border-radius: 3px;
  }
  
  .recent-activity-list::-webkit-scrollbar-thumb:hover {
    background-color: #a0aec0;
  }
  

  
