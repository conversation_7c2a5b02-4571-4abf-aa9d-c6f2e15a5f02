<div class="dashboard-container">
  <h2 class="fw-bold mb-5">Admin Overview</h2>

  <div class="stats">
    <div class="stat-card">
      <h4><i class="fas fa-file-alt"></i> Total Exams</h4>
      <div class="stat-number blue">{{exams.length}}</div>
    </div>
    <div class="stat-card">
      <h4><i class="fas fa-question-circle"></i> Total Questions</h4>
      <div class="stat-number green">{{totalQuestions}}</div>
    </div>
  </div>

  <div class="quick-actions">
    <div class="action-box">
      <h4>Quick Actions</h4>
      <a [routerLink]="['/admin/exam-form']" class="action-link primary-action"><i class="fas fa-plus"></i> Create New
        Exam</a>
      <a [routerLink]="['/admin/question-form']" class="action-link"><i class="fas fa-plus"></i> Add Questions</a>
      <a [routerLink]="['/admin/results']" class="action-link"><i class="fas fa-chart-line"></i> View Student
        Results</a>
    </div>

    <div class="activity-box">
      <h4><i class="fas fa-clock"></i> Recent Activity</h4>
      <ul class="recent-activity-list">
        <li *ngFor="let activity of recentActivities">
          <i class="fas fa-check-circle text-green-500"></i> {{ activity.message }}
        </li>
        <li *ngIf="recentActivities.length === 0" class="text-muted">
          No recent activity to display.
        </li>
      </ul>
    </div>
  </div>

  <div class="exams-list mt-4">
    <h4>All Exams</h4>
    <table class="table table-striped">
      <thead>
        <tr>
          <th>Title</th>
          <th>Description</th>
          <th>Questions</th>
          <th>Duration</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let exam of exams">
          <td>{{ exam.title }}</td>
          <td>{{ exam.description }}</td>
          <td>{{ exam.questions.length }}</td>
          <td>{{ exam.duration }} mins</td>
          <td>
            <a [routerLink]="['/admin/exams', exam.id, 'edit']" class="btn btn-sm btn-primary">Edit</a>
            <button (click)="deleteExam(exam.id)" class="btn btn-sm btn-danger ms-2">Delete</button>
          </td>
        </tr>
      </tbody>
    </table>
    <div *ngIf="exams.length === 0" class="text-muted">No exams found.</div>
  </div>
</div>