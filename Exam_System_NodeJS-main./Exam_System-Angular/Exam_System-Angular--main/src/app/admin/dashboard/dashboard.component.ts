import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ExamService } from '../../services/exam.service';
import { Exam } from '../../models/exam.model';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [RouterModule,CommonModule],
  templateUrl: './dashboard.html',
  styleUrls: ['./dashboard.css'],
})
export class AdminDashboardComponent implements OnInit {
  exams: Exam[] = [];
  totalQuestions: number = 0;
  recentActivities: { message: string; time: Date }[] = [];

  constructor(private examService: ExamService) {}

  ngOnInit(): void {
    this.loadExams();
  }

  loadExams(): void {
    const token = localStorage.getItem('token') || '';
    this.examService.getExams(token).subscribe({
      next: (exams) => {
        this.exams = exams;
        this.totalQuestions = this.calculateTotalQuestions(this.exams);
        this.recentActivities = this.exams.flatMap((e) => {
          if (e.updatedAt && e.updatedAt !== e.createdAt) {
            return [{
              message: `Updated exam "${e.title}"`,
              time: new Date(e.updatedAt),
            }];
          } else {
            return [{
              message: `Created exam "${e.title}"`,
              time: new Date(e.createdAt),
            }];
          }
        })
        .sort((a, b) => b.time.getTime() - a.time.getTime())
        .slice(0, 4);
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  deleteExam(examId: string): void {
    if (confirm('Are you sure you want to delete this exam?')) {
      const token = localStorage.getItem('token') || '';
      this.examService.deleteExam(examId, token).subscribe({
        next: () => {
          this.loadExams();
        },
        error: (err) => {
          alert('Failed to delete exam.');
        }
      });
    }
  }

  private calculateTotalQuestions(exams: Exam[]): number {
    return exams.reduce(
      (total, exam) => total + (exam.questions?.length || 0),
      0
    );
  }
}
