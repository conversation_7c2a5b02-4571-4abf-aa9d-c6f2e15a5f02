<div class="exam-form-container">
  <h2>{{ isEditMode ? 'Edit Exam' : 'Create Exam' }}</h2>
  <form (ngSubmit)="onSubmit()">
    <div>
      <label for="title">Title:</label>
      <input id="title" [(ngModel)]="exam.title" name="title" required />
    </div>
    <div>
      <label for="description">Description:</label>
      <textarea id="description" [(ngModel)]="exam.description" name="description"></textarea>
    </div>
    <div>
      <label for="duration">Duration (minutes):</label>
      <input id="duration" type="number" [(ngModel)]="exam.duration" name="duration" required />
    </div>
    <div>
      <label for="passingScore">Passing Score (%):</label>
      <input id="passingScore" type="number" [(ngModel)]="exam.passingScore" name="passingScore" required />
    </div>
    <div>
      <label>
        <input type="checkbox" [(ngModel)]="exam.isActive" name="isActive" />
        Active
      </label>
    </div>
    <div class="form-actions">
      <button type="submit">{{ isEditMode ? 'Update' : 'Create' }}</button>
      <button type="button" (click)="cancel()">Cancel</button>
    </div>
  </form>
</div>