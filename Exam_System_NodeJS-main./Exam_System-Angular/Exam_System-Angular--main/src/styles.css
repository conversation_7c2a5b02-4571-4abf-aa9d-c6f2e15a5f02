/* Global styles for exam system */
:root {
  /* Color palette */
  --primary: #3366CC;
  --primary-light: #6691E7;
  --primary-dark: #0E4394;
  
  --secondary: #4CAF50;
  --secondary-light: #7FE183;
  --secondary-dark: #1B7D20;
  
  --accent: #9C27B0;
  --accent-light: #CE5FE0;
  --accent-dark: #6A0080;
  
  --success: #43A047;
  --warning: #FFA000;
  --error: #E53935;
  
  --neutral-50: #FAFAFA;
  --neutral-100: #F5F5F5;
  --neutral-200: #EEEEEE;
  --neutral-300: #E0E0E0;
  --neutral-400: #BDBDBD;
  --neutral-500: #9E9E9E;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;
  
  /* Spacing (8px grid) */
  --space-1: 8px;
  --space-2: 16px;
  --space-3: 24px;
  --space-4: 32px;
  --space-5: 40px;
  --space-6: 48px;
  
  /* Typography */
  --font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  font-size: 16px;
  line-height: 1.5;
  color: var(--neutral-800);
  background-color: var(--neutral-100);
}

h1, h2, h3, h4, h5, h6 {
  line-height: 1.2;
  color: var(--neutral-900);
  margin-bottom: var(--space-2);
}

h1 {
  font-size: 2.25rem;
}

h2 {
  font-size: 1.875rem;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-dark);
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-2);
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: var(--space-3);
  margin-bottom: var(--space-3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.button {
  display: inline-block;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: var(--space-1) var(--space-2);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.button:hover {
  background-color: var(--primary-dark);
}

.button.secondary {
  background-color: var(--secondary);
}

.button.secondary:hover {
  background-color: var(--secondary-dark);
}

.button.accent {
  background-color: var(--accent);
}

.button.accent:hover {
  background-color: var(--accent-dark);
}

.button.outlined {
  background-color: transparent;
  border: 1px solid var(--primary);
  color: var(--primary);
}

.button.outlined:hover {
  background-color: rgba(51, 102, 204, 0.1);
}

.form-group {
  margin-bottom: var(--space-2);
}

.form-label {
  display: block;
  margin-bottom: var(--space-1);
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: var(--space-1);
  border: 1px solid var(--neutral-300);
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(51, 102, 204, 0.2);
}

.badge {
  display: inline-block;
  padding: 0.25em 0.75em;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 1rem;
  background-color: var(--primary-light);
  color: white;
}

.badge.success {
  background-color: var(--success);
}

.badge.warning {
  background-color: var(--warning);
}

.badge.error {
  background-color: var(--error);
}

.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.grid {
  display: grid;
  grid-gap: var(--space-3);
}

/* Responsive grid */
@media (min-width: 768px) {
  .grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.4s ease-out;
}
