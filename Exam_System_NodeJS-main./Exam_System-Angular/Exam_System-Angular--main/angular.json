{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"my-angular-app": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": "dist/my-angular-app", "index": "src/index.html", "browser": "src/main.ts", "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.css"], "polyfills": ["src/polyfills.ts"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "my-angular-app:build:production"}, "development": {"buildTarget": "my-angular-app:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.css"]}}}}}}