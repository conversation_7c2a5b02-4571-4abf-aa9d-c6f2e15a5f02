{"name": "token-stream", "version": "1.0.0", "description": "Take an array of token and produce a more useful API to give to a parser", "dependencies": {}, "devDependencies": {"istanbul": "*"}, "scripts": {"test": "node test && npm run coverage", "coverage": "istanbul cover test"}, "repository": {"type": "git", "url": "https://github.com/pugjs/token-stream.git"}, "author": "ForbesLindesay", "license": "MIT"}