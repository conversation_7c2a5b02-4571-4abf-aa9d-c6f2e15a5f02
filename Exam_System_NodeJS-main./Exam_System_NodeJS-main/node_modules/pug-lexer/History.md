2.3.0 / 2016-09-11
==================

  * Update is-expression to 3.0.0

2.2.2 / 2016-09-07
==================

  * Support non-standard class names that start with two hyphens in class
    literals, most notably used in Bemto

2.2.1 / 2016-08-29
==================

  * Fix semantics of `isExpression` plugin

2.2.0 / 2016-08-26
==================

  * Allow customizing `isExpression`

2.1.0 / 2016-08-22
==================

  * Allow attributes that start with a colon

2.0.3 / 2016-08-07
==================

  * Allow `when` expressions with colons
  * Fix incorrect location of some errors

2.0.2 / 2016-06-02
==================

  * Fix incorrect location of some invalid expressions in an attribute.

2.0.1 / 2016-05-31
==================

  * Update README for `filename` option

2.0.0 / 2016-05-14
==================

  * Take the `filename` as an option rather than special casing it.  This means that lex only takes 2 arguments rather than 3
  * Add support for an inline comment after a block.  This means block names can no longer contain `//`
  * Add type checking on arguments

1.2.0 / 2016-05-14
==================

  * Throw a more helpful error if someone attempts to use the old `- each foo in bar` syntax (it should not have the `- ` prefix)
  * Add Error reporting for invalid case expressions

1.0.1 / 2016-04-18
==================

  * Update dependencies
    - Update to `is-expression@2` which allows ES2015-style template strings
      by default.

1.0.0 / 2015-12-23
==================

  * First stable release
