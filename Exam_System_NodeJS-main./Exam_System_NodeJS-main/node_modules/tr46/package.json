{"name": "tr46", "version": "5.1.1", "engines": {"node": ">=18"}, "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "main": "index.js", "files": ["index.js", "lib/"], "scripts": {"test": "node --test", "lint": "eslint", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "repository": "https://github.com/jsdom/tr46", "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "devDependencies": {"@domenic/eslint-config": "^4.0.1", "@unicode/unicode-16.0.0": "^1.6.5", "eslint": "^9.22.0", "globals": "^16.0.0", "regenerate": "^1.4.2"}, "unicodeVersion": "16.0.0"}