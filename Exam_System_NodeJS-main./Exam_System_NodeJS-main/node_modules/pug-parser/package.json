{"name": "pug-parser", "version": "6.0.0", "description": "The pug parser (takes an array of tokens and converts it to an abstract syntax tree)", "keywords": ["pug"], "dependencies": {"pug-error": "^2.0.0", "token-stream": "1.0.0"}, "devDependencies": {}, "files": ["lib/inline-tags.js", "index.js"], "repository": {"type": "git", "url": "https://github.com/pugjs/pug/tree/master/packages/pug-parser"}, "author": "ForbesLindesay", "license": "MIT"}