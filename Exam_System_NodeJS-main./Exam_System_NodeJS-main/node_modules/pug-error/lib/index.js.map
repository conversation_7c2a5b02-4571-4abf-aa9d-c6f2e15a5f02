{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;AAAA,SAAwB,SAAS,CAC/B,IAAY,EACZ,OAAe,EACf,OAAwE;IAExE,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC1B,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAC9B,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IAClC,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IACxB,IAAI,WAAW,CAAC;IAChB,IAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACrD,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;QACtD,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAM,OAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;QAC7C,gBAAgB;QAChB,IAAM,OAAO,GAAG,KAAK;aAClB,KAAK,CAAC,OAAK,EAAE,GAAG,CAAC;aACjB,GAAG,CAAC,UAAS,IAAI,EAAE,CAAC;YACnB,IAAM,IAAI,GAAG,CAAC,GAAG,OAAK,GAAG,CAAC,CAAC;YAC3B,IAAM,QAAQ,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;YAChE,IAAI,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC;YAC1B,IAAI,IAAI,KAAK,IAAI,IAAI,MAAM,GAAG,CAAC,EAAE;gBAC/B,GAAG,IAAI,IAAI,CAAC;gBACZ,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;aACxD;YACD,OAAO,GAAG,CAAC;QACb,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAC;QACd,WAAW;YACT,CAAC,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAG,IAAI,GAAG,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;KAC5E;SAAM;QACL,WAAW,GAAG,CAAC,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC;KACvE;IACD,IAAM,GAAG,GAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;IACxC,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC;IACzB,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC;IAClB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;IAChB,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;IACpB,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACxB,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;IACd,GAAG,CAAC,MAAM,GAAG;QACX,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;IACJ,CAAC,CAAC;IACF,OAAO,GAAG,CAAC;AACb,CAAC;AAnDD,4BAmDC;AAED,wCAAwC;AACxC,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;AAC3B,MAAM,CAAC,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC"}