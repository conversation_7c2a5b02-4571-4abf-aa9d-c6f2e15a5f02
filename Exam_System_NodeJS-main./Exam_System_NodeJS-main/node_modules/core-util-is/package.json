{"name": "core-util-is", "version": "1.0.3", "description": "The `util.is*` functions introduced in Node v0.12.", "main": "lib/util.js", "files": ["lib"], "repository": {"type": "git", "url": "git://github.com/isaacs/core-util-is"}, "keywords": ["util", "<PERSON><PERSON><PERSON><PERSON>", "isArray", "isNumber", "isString", "isRegExp", "isThis", "isThat", "polyfill"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "MIT", "bugs": {"url": "https://github.com/isaacs/core-util-is/issues"}, "scripts": {"test": "tap test.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "devDependencies": {"tap": "^15.0.9"}}