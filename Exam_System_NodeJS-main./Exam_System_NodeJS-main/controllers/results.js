const resultModel = require("../models/results");
const AppError = require("../utils/AppError");
const { CatchAsync } = require("../utils/CatchAsync");

const getAllResults = CatchAsync(async (req, res, next) => {
  let results = await resultModel
    .find()
    .populate("examId", "title -_id")
    .populate("studentId", "username email -_id")
    .select("-_id -updatedAt -__v");
  res.status(200).json({
    status: "Success",
    data: results,
  });
});

const getStudentResults = CatchAsync(async (req, res, next) => {
  const studentId = req.id;
  let results = await resultModel
    .find({ studentId })
    .populate("examId", "title -_id")
    .select("percentage examId -_id");
  if (results) {
    res.status(200).json({
      status: "success",
      data: results,
    });
  } else {
    next(new AppError(400, "Result not found"));
  }
});

module.exports = { getAllResults, getStudentResults };
