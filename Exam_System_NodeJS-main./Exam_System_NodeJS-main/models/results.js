const mongoose = require("mongoose");

const resultSchema = mongoose.Schema(
  {
    studentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    examId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Exam",
      required: true,
    },    
    score: {
      type: Number,
      min: 0,
    },    
    percentage: {
      type: Number,
      min: 0,
      max:100
    }},
  {
    timestamps: true,
  }
);
const resultModel = mongoose.model("Result", resultSchema);
module.exports = resultModel;
