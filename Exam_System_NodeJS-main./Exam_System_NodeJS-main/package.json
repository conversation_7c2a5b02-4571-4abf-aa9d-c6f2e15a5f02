{"name": "day-2", "version": "1.0.0", "description": "", "license": "ISC", "author": "", "type": "commonjs", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon index"}, "dependencies": {"accepts": "^2.0.0", "anymatch": "^3.1.3", "balanced-match": "^1.0.2", "bcryptjs": "^3.0.2", "binary-extensions": "^2.3.0", "body-parser": "^2.2.0", "brace-expansion": "^1.1.11", "braces": "^3.0.3", "bytes": "^3.1.2", "call-bind-apply-helpers": "^1.0.2", "call-bound": "^1.0.4", "chokidar": "^3.6.0", "concat-map": "^0.0.1", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.2", "cookie-signature": "^1.2.2", "cors": "^2.8.5", "debug": "^4.4.0", "depd": "^2.0.0", "dotenv": "^16.5.0", "dunder-proto": "^1.0.1", "ee-first": "^1.1.1", "encodeurl": "^2.0.0", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "escape-html": "^1.0.3", "etag": "^1.8.1", "express": "^5.1.0", "fill-range": "^7.1.1", "finalhandler": "^2.1.0", "forwarded": "^0.2.0", "fresh": "^2.0.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "glob-parent": "^5.1.2", "gopd": "^1.2.0", "has-flag": "^3.0.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "ignore-by-default": "^1.0.1", "inherits": "^2.0.4", "ipaddr.js": "^1.9.1", "is-binary-path": "^2.1.0", "is-extglob": "^2.1.1", "is-glob": "^4.0.3", "is-number": "^7.0.0", "is-promise": "^4.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "math-intrinsics": "^1.1.0", "media-typer": "^1.1.0", "merge-descriptors": "^2.0.0", "mime-db": "^1.54.0", "mime-types": "^3.0.1", "minimatch": "^3.1.2", "mongodb": "^6.16.0", "mongoose": "^8.14.0", "ms": "^2.1.3", "multer": "^1.4.5-lts.2", "negotiator": "^1.0.0", "normalize-path": "^3.0.0", "object-inspect": "^1.13.4", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.2.0", "picomatch": "^2.3.1", "proxy-addr": "^2.0.7", "pstree.remy": "^1.1.8", "pug": "^3.0.3", "qs": "^6.14.0", "range-parser": "^1.2.1", "raw-body": "^3.0.0", "readdirp": "^3.6.0", "router": "^2.2.0", "safe-buffer": "^5.2.1", "safer-buffer": "^2.1.2", "semver": "^7.7.1", "send": "^1.2.0", "serve-static": "^2.2.0", "setprototypeof": "^1.2.0", "side-channel": "^1.1.0", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2", "simple-update-notifier": "^2.0.0", "statuses": "^2.0.1", "supports-color": "^5.5.0", "to-regex-range": "^5.0.1", "toidentifier": "^1.0.1", "touch": "^3.1.1", "type-is": "^2.0.1", "undefsafe": "^2.0.5", "unpipe": "^1.0.0", "vary": "^1.1.2", "wrappy": "^1.0.2"}, "devDependencies": {"nodemon": "^3.1.10"}}